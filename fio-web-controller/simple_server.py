#!/usr/bin/env python3
"""
简化版FIO Web控制器 - 不依赖外部包
使用Python内置的http.server和基本的CGI功能
"""

import http.server
import socketserver
import json
import subprocess
import threading
import time
import os
import sys
import urllib.parse
from datetime import datetime
import signal

# 全局变量
current_process = None
test_status = {
    'running': False,
    'stage': '',
    'progress': 0,
    'start_time': None,
    'logs': []
}

class FIOTestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        if self.path == '/':
            self.serve_index()
        elif self.path == '/api/status':
            self.serve_status()
        elif self.path == '/api/config':
            self.serve_config()
        elif self.path == '/api/devices':
            self.serve_devices()
        else:
            super().do_GET()
    
    def do_POST(self):
        if self.path == '/api/start':
            self.handle_start()
        elif self.path == '/api/stop':
            self.handle_stop()
        else:
            self.send_error(404)
    
    def serve_index(self):
        """提供主页"""
        try:
            with open('templates/index_simple.html', 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_error(404, "页面未找到")
    
    def serve_status(self):
        """提供测试状态"""
        self.send_json_response(test_status)
    
    def serve_config(self):
        """提供默认配置"""
        config = {
            'target_device': '/dev/nvme1n1',
            'precondition_bs': '2M',
            'precondition_runtime': '1800s',
            'block_sizes': ['4k', '128k', '1M'],
            'rw_mix_read_percent': 80,
            'runtime': 3600,
            'ramp_time': 30,
            'num_jobs': 56,
            'iodepth_per_job': 9,
            'log_interval': 5000
        }
        self.send_json_response(config)
    
    def serve_devices(self):
        """获取可用设备列表"""
        try:
            result = subprocess.run(['lsblk', '-d', '-n', '-o', 'NAME,SIZE,TYPE'],
                                  capture_output=True, text=True, timeout=10)
            devices = []
            if result.returncode == 0 and result.stdout:
                for line in result.stdout.strip().split('\n'):
                    if line and 'disk' in line:
                        parts = line.split()
                        if len(parts) >= 3:
                            devices.append({
                                'name': f'/dev/{parts[0]}',
                                'size': parts[1],
                                'type': parts[2]
                            })

            # 如果没有找到设备，添加默认设备
            if not devices:
                devices.append({
                    'name': '/dev/nvme1n1',
                    'size': '未知',
                    'type': 'disk'
                })

            self.send_json_response(devices)
        except Exception as e:
            # 返回默认设备列表
            default_devices = [
                {'name': '/dev/nvme1n1', 'size': '未知', 'type': 'disk'},
                {'name': '/dev/sda', 'size': '未知', 'type': 'disk'}
            ]
            self.send_json_response(default_devices)
    
    def handle_start(self):
        """处理启动测试请求"""
        global test_status
        
        if test_status['running']:
            self.send_json_response({'error': '测试正在运行中'}, 400)
            return
        
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            config = json.loads(post_data.decode('utf-8'))
            
            # 启动测试
            test_runner = TestRunner()
            test_runner.run_test(config)
            
            self.send_json_response({'message': '测试已启动'})
            
        except Exception as e:
            self.send_json_response({'error': str(e)}, 500)
    
    def handle_stop(self):
        """处理停止测试请求"""
        global current_process
        
        if current_process and current_process.poll() is None:
            current_process.terminate()
            time.sleep(2)
            if current_process.poll() is None:
                current_process.kill()
        
        test_status['running'] = False
        test_status['stage'] = '测试已停止'
        
        self.send_json_response({'message': '测试已停止'})
    
    def send_json_response(self, data, status=200):
        """发送JSON响应"""
        json_data = json.dumps(data, ensure_ascii=False, indent=2)
        
        self.send_response(status)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(json_data.encode('utf-8'))

class TestRunner:
    def __init__(self):
        self.process = None
        self.thread = None
        
    def run_test(self, config):
        """运行FIO测试"""
        global test_status, current_process
        
        # 重置状态
        test_status = {
            'running': True,
            'stage': '准备测试',
            'progress': 0,
            'start_time': datetime.now().isoformat(),
            'logs': []
        }
        
        # 创建修改后的脚本
        script_content = self.generate_script(config)
        script_path = '/tmp/fio_test_custom.sh'
        
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        os.chmod(script_path, 0o755)
        
        # 在新线程中运行脚本
        self.thread = threading.Thread(target=self._execute_script, args=(script_path,))
        self.thread.start()
        
    def _execute_script(self, script_path):
        """在后台执行脚本"""
        global test_status, current_process
        
        try:
            # 启动脚本进程
            current_process = subprocess.Popen(
                ['/bin/bash', script_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 实时读取输出
            for line in iter(current_process.stdout.readline, ''):
                if line:
                    log_entry = {
                        'timestamp': datetime.now().isoformat(),
                        'message': line.strip()
                    }
                    test_status['logs'].append(log_entry)
                    
                    # 分析进度
                    self._analyze_progress(line.strip())
                    
                    # 限制日志数量
                    if len(test_status['logs']) > 1000:
                        test_status['logs'] = test_status['logs'][-500:]
            
            # 等待进程完成
            current_process.wait()
            
            if current_process.returncode == 0:
                test_status['stage'] = '测试完成'
                test_status['progress'] = 100
            else:
                test_status['stage'] = '测试失败'
                
        except Exception as e:
            test_status['stage'] = f'错误: {str(e)}'
            test_status['logs'].append({
                'timestamp': datetime.now().isoformat(),
                'message': f'执行错误: {str(e)}'
            })
        finally:
            test_status['running'] = False
            
    def _analyze_progress(self, line):
        """分析日志行来更新进度"""
        global test_status
        
        if '阶段 1/3' in line:
            test_status['stage'] = '硬盘预写'
            test_status['progress'] = 10
        elif '阶段 2/3' in line:
            test_status['stage'] = '性能测试'
            test_status['progress'] = 40
        elif '阶段 3/3' in line:
            test_status['stage'] = '生成图表'
            test_status['progress'] = 80
        elif '所有测试已全部完成' in line:
            test_status['stage'] = '测试完成'
            test_status['progress'] = 100
            
    def generate_script(self, config):
        """根据配置生成脚本内容"""
        script_template = '''#!/bin/bash

# FIO NVMe 测试脚本 - Web控制版本

TARGET_DEVICE="{target_device}"
OUTPUT_DIR="./fio_results_$(date +%Y%m%d_%H%M%S)"

# 预写配置
PRECONDITION_BS="{precondition_bs}"
PRECONDITION_RUNTIME="{precondition_runtime}"

# 性能测试配置
BLOCK_SIZES=({block_sizes})
RW_MIX_READ_PERCENT={rw_mix_read_percent}
RUNTIME={runtime}
RAMP_TIME={ramp_time}
NUM_JOBS={num_jobs}
IODEPTH_PER_JOB={iodepth_per_job}
LOG_INTERVAL={log_interval}

# 检查目标设备
if [ ! -b "$TARGET_DEVICE" ]; then
    echo "错误: 设备 $TARGET_DEVICE 不存在或不是一个块设备。"
    exit 1
fi

# 创建结果目录
mkdir -p "$OUTPUT_DIR"
echo "测试结果将保存在: $OUTPUT_DIR"
echo "---------------------------------------------------------"

# 阶段 1: 硬盘预写
echo "[阶段 1/3] 开始进行硬盘预写，让其进入稳定态..."
echo "  - 块大小: $PRECONDITION_BS"
echo "  - 模式: 顺序写"
echo "  - 时长: $PRECONDITION_RUNTIME"

fio --name=precondition \\
    --filename=$TARGET_DEVICE \\
    --direct=1 \\
    --ioengine=libaio \\
    --rw=write \\
    --bs=$PRECONDITION_BS \\
    --runtime=$PRECONDITION_RUNTIME \\
    --time_based \\
    --numjobs=1 \\
    --iodepth=16

echo "[阶段 1/3] 硬盘预写完成。等待10秒让主控稳定..."
sleep 10

# 阶段 2: 核心性能基准测试
echo "[阶段 2/3] 开始进行核心性能基准测试..."
FIO_BASE_OPTS="--filename=$TARGET_DEVICE --direct=1 --ioengine=libaio --group_reporting --time_based --norandommap"
FIO_WORKLOAD_OPTS="--rw=randrw --rwmixread=$RW_MIX_READ_PERCENT --numjobs=$NUM_JOBS --iodepth=$IODEPTH_PER_JOB --runtime=$RUNTIME --ramp_time=$RAMP_TIME"

for bs in "${{BLOCK_SIZES[@]}}"; do
    echo "--> 正在测试: 80/20 混合随机IO, 块大小: $bs"
    
    LOG_PREFIX="$OUTPUT_DIR/mixed_rw_${{bs}}"
    
    fio --name="mixed_rw_${{bs}}" \\
        ${{FIO_BASE_OPTS}} \\
        ${{FIO_WORKLOAD_OPTS}} \\
        --bs=$bs \\
        --write_bw_log="${{LOG_PREFIX}}" \\
        --write_iops_log="${{LOG_PREFIX}}" \\
        --log_avg_msec=$LOG_INTERVAL \\
        --output="${{LOG_PREFIX}}_summary.txt"

    echo "--> 测试完成: 块大小 $bs"
    sleep 5
done

echo "[阶段 2/3] 所有基准测试完成。"
echo "[阶段 3/3] 测试完成，跳过图表生成"
echo "所有测试已全部完成！"
echo "详细的摘要和日志数据保存在目录: $OUTPUT_DIR"
'''.format(
            target_device=config.get('target_device', '/dev/nvme1n1'),
            precondition_bs=config.get('precondition_bs', '2M'),
            precondition_runtime=config.get('precondition_runtime', '1800s'),
            block_sizes=' '.join([f'"{bs}"' for bs in config.get('block_sizes', ['4k', '128k', '1M'])]),
            rw_mix_read_percent=config.get('rw_mix_read_percent', 80),
            runtime=config.get('runtime', 3600),
            ramp_time=config.get('ramp_time', 30),
            num_jobs=config.get('num_jobs', 56),
            iodepth_per_job=config.get('iodepth_per_job', 9),
            log_interval=config.get('log_interval', 5000)
        )
        
        return script_template

def signal_handler(sig, frame):
    """信号处理器"""
    print('\n正在关闭服务器...')
    if current_process and current_process.poll() is None:
        current_process.terminate()
    sys.exit(0)

def main():
    PORT = 5000
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建HTTP服务器
    with socketserver.TCPServer(("0.0.0.0", PORT), FIOTestHandler) as httpd:
        print("=== FIO Web Controller (简化版) ===")
        print(f"服务器启动在端口 {PORT}")
        print(f"本地访问: http://localhost:{PORT}")
        
        # 获取本机IP
        try:
            import socket
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            print(f"局域网访问: http://{local_ip}:{PORT}")
        except:
            pass
            
        print("按 Ctrl+C 停止服务器")
        print("=" * 35)
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n服务器已停止")

if __name__ == '__main__':
    main()
