<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FIO NVMe 硬盘测试控制器</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/uiverse.css') }}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🚀 FIO NVMe 硬盘测试控制器</h1>
            <p>通过Web界面控制和监控硬盘性能测试</p>
        </header>

        <div class="main-content">
            <!-- 配置面板 -->
            <div class="config-panel">
                <h2>📋 测试配置</h2>
                
                <div class="form-group">
                    <label for="target_device">目标设备:</label>
                    <select id="target_device" class="form-control">
                        <option value="/dev/nvme1n1">/dev/nvme1n1</option>
                    </select>
                    <button id="refresh-devices" class="btn btn-secondary">刷新设备</button>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="precondition_bs">预写块大小:</label>
                        <select id="precondition_bs" class="form-control">
                            <option value="1M">1M</option>
                            <option value="2M" selected>2M</option>
                            <option value="4M">4M</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="precondition_runtime">预写时长:</label>
                        <select id="precondition_runtime" class="form-control">
                            <option value="300s">5分钟</option>
                            <option value="900s">15分钟</option>
                            <option value="1800s" selected>30分钟</option>
                            <option value="3600s">60分钟</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label>测试块大小:</label>
                    <div class="checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" value="4k" checked> 4K
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" value="128k" checked> 128K
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" value="1M" checked> 1M
                        </label>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="rw_mix_read_percent">读写比例 (读%):</label>
                        <input type="range" id="rw_mix_read_percent" min="0" max="100" value="80" class="slider">
                        <span id="rw_mix_display">80% 读 / 20% 写</span>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="runtime">测试时长 (秒):</label>
                        <input type="number" id="runtime" value="3600" min="60" max="86400" class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <label for="num_jobs">并发线程数:</label>
                        <input type="number" id="num_jobs" value="56" min="1" max="128" class="form-control">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="iodepth_per_job">每线程队列深度:</label>
                        <input type="number" id="iodepth_per_job" value="9" min="1" max="32" class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <label for="ramp_time">预热时间 (秒):</label>
                        <input type="number" id="ramp_time" value="30" min="0" max="300" class="form-control">
                    </div>
                </div>

                <div class="control-buttons">
                    <button id="start-test" class="btn btn-primary">🚀 开始测试</button>
                    <button id="stop-test" class="btn btn-danger" disabled>⏹️ 停止测试</button>
                </div>
            </div>

            <!-- 状态面板 -->
            <div class="status-panel">
                <h2>📊 测试状态</h2>
                
                <div class="status-info">
                    <div class="status-item">
                        <span class="label">状态:</span>
                        <span id="test-status" class="value">待机</span>
                    </div>
                    <div class="status-item">
                        <span class="label">当前阶段:</span>
                        <span id="test-stage" class="value">-</span>
                    </div>
                    <div class="status-item">
                        <span class="label">开始时间:</span>
                        <span id="start-time" class="value">-</span>
                    </div>
                </div>

                <div class="progress-container">
                    <div class="progress-bar">
                        <div id="progress-fill" class="progress-fill"></div>
                    </div>
                    <span id="progress-text" class="progress-text">0%</span>
                </div>

                <!-- 日志面板 -->
                <div class="log-panel">
                    <h3>📝 实时日志</h3>
                    <div class="log-controls">
                        <button id="clear-logs" class="btn btn-secondary">清空日志</button>
                        <label class="checkbox-label">
                            <input type="checkbox" id="auto-scroll" checked> 自动滚动
                        </label>
                    </div>
                    <div id="log-container" class="log-container"></div>
                </div>
            </div>
        </div>

        <!-- 警告对话框 -->
        <div id="warning-modal" class="modal">
            <div class="modal-content">
                <h3>⚠️ 重要警告</h3>
                <p>此测试将对选定的硬盘设备进行<strong>破坏性读写操作</strong>！</p>
                <p>设备上的<strong>所有数据都将丢失</strong>！</p>
                <p>请确认您选择的设备是正确的，并且已经备份了重要数据。</p>
                <div class="modal-buttons">
                    <button id="confirm-test" class="btn btn-danger">确认开始测试</button>
                    <button id="cancel-test" class="btn btn-secondary">取消</button>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
