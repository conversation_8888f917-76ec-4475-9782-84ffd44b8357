<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FIO NVMe 硬盘测试控制器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }

        .panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .panel h2 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.5rem;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #4a5568;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .checkbox-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .checkbox-label input {
            margin-right: 8px;
            transform: scale(1.2);
        }

        .slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #e2e8f0;
            outline: none;
            -webkit-appearance: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 10px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .status-info {
            margin-bottom: 25px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .status-item .label {
            font-weight: 600;
            color: #4a5568;
        }

        .progress-container {
            margin-bottom: 25px;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.5s ease;
        }

        .log-container {
            height: 300px;
            background: #1a202c;
            border-radius: 8px;
            padding: 15px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            color: #e2e8f0;
            border: 1px solid #2d3748;
        }

        .log-entry {
            margin-bottom: 5px;
            line-height: 1.4;
        }

        .log-timestamp {
            color: #a0aec0;
            margin-right: 10px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }

        .modal-content h3 {
            color: #e53e3e;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            margin-top: 25px;
            justify-content: flex-end;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-indicator.running {
            background: #48bb78;
            animation: pulse 1s infinite;
        }

        .status-indicator.idle {
            background: #a0aec0;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🚀 FIO NVMe 硬盘测试控制器</h1>
            <p>通过Web界面控制和监控硬盘性能测试</p>
        </header>

        <div class="main-content">
            <!-- 配置面板 -->
            <div class="panel">
                <h2>📋 测试配置</h2>
                
                <div class="form-group">
                    <label for="target_device">目标设备:</label>
                    <select id="target_device" class="form-control">
                        <option value="/dev/nvme1n1">/dev/nvme1n1</option>
                    </select>
                    <button id="refresh-devices" class="btn btn-secondary" style="margin-top: 10px;">刷新设备</button>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="precondition_bs">预写块大小:</label>
                        <select id="precondition_bs" class="form-control">
                            <option value="1M">1M</option>
                            <option value="2M" selected>2M</option>
                            <option value="4M">4M</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="precondition_runtime">预写时长:</label>
                        <select id="precondition_runtime" class="form-control">
                            <option value="300s">5分钟</option>
                            <option value="900s">15分钟</option>
                            <option value="1800s" selected>30分钟</option>
                            <option value="3600s">60分钟</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label>测试块大小:</label>
                    <div class="checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" value="4k" checked> 4K
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" value="128k" checked> 128K
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" value="1M" checked> 1M
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label for="rw_mix_read_percent">读写比例 (读%):</label>
                    <input type="range" id="rw_mix_read_percent" min="0" max="100" value="80" class="slider">
                    <span id="rw_mix_display">80% 读 / 20% 写</span>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="runtime">测试时长 (秒):</label>
                        <input type="number" id="runtime" value="3600" min="60" max="86400" class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <label for="num_jobs">并发线程数:</label>
                        <input type="number" id="num_jobs" value="56" min="1" max="128" class="form-control">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="iodepth_per_job">每线程队列深度:</label>
                        <input type="number" id="iodepth_per_job" value="9" min="1" max="32" class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <label for="ramp_time">预热时间 (秒):</label>
                        <input type="number" id="ramp_time" value="30" min="0" max="300" class="form-control">
                    </div>
                </div>

                <div style="margin-top: 25px;">
                    <button id="start-test" class="btn btn-primary">🚀 开始测试</button>
                    <button id="stop-test" class="btn btn-danger" disabled>⏹️ 停止测试</button>
                </div>
            </div>

            <!-- 状态面板 -->
            <div class="panel">
                <h2>📊 测试状态</h2>
                
                <div class="status-info">
                    <div class="status-item">
                        <span class="label">状态:</span>
                        <span id="test-status">
                            <span class="status-indicator idle"></span>待机
                        </span>
                    </div>
                    <div class="status-item">
                        <span class="label">当前阶段:</span>
                        <span id="test-stage">-</span>
                    </div>
                    <div class="status-item">
                        <span class="label">开始时间:</span>
                        <span id="start-time">-</span>
                    </div>
                </div>

                <div class="progress-container">
                    <div class="progress-bar">
                        <div id="progress-fill" class="progress-fill"></div>
                    </div>
                    <span id="progress-text">0%</span>
                </div>

                <!-- 日志面板 -->
                <div>
                    <h3 style="color: #4a5568; margin-bottom: 15px;">📝 实时日志</h3>
                    <button id="clear-logs" class="btn btn-secondary" style="margin-bottom: 15px;">清空日志</button>
                    <div id="log-container" class="log-container"></div>
                </div>
            </div>
        </div>

        <!-- 警告对话框 -->
        <div id="warning-modal" class="modal">
            <div class="modal-content">
                <h3>⚠️ 重要警告</h3>
                <p>此测试将对选定的硬盘设备进行<strong>破坏性读写操作</strong>！</p>
                <p>设备上的<strong>所有数据都将丢失</strong>！</p>
                <p>请确认您选择的设备是正确的，并且已经备份了重要数据。</p>
                <div class="modal-buttons">
                    <button id="confirm-test" class="btn btn-danger">确认开始测试</button>
                    <button id="cancel-test" class="btn btn-secondary">取消</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简化版JavaScript应用
        class FIOController {
            constructor() {
                this.testRunning = false;
                this.statusInterval = null;
                
                this.initializeElements();
                this.bindEvents();
                this.loadDevices();
                this.startStatusPolling();
            }
            
            initializeElements() {
                // 表单元素
                this.targetDevice = document.getElementById('target_device');
                this.preconditionBs = document.getElementById('precondition_bs');
                this.preconditionRuntime = document.getElementById('precondition_runtime');
                this.rwMixReadPercent = document.getElementById('rw_mix_read_percent');
                this.rwMixDisplay = document.getElementById('rw_mix_display');
                this.runtime = document.getElementById('runtime');
                this.numJobs = document.getElementById('num_jobs');
                this.iodepthPerJob = document.getElementById('iodepth_per_job');
                this.rampTime = document.getElementById('ramp_time');
                
                // 控制按钮
                this.startButton = document.getElementById('start-test');
                this.stopButton = document.getElementById('stop-test');
                this.refreshDevicesButton = document.getElementById('refresh-devices');
                this.clearLogsButton = document.getElementById('clear-logs');
                
                // 状态显示
                this.testStatus = document.getElementById('test-status');
                this.testStage = document.getElementById('test-stage');
                this.startTime = document.getElementById('start-time');
                this.progressFill = document.getElementById('progress-fill');
                this.progressText = document.getElementById('progress-text');
                this.logContainer = document.getElementById('log-container');
                
                // 模态框
                this.warningModal = document.getElementById('warning-modal');
                this.confirmButton = document.getElementById('confirm-test');
                this.cancelButton = document.getElementById('cancel-test');
            }
            
            bindEvents() {
                // 滑块事件
                this.rwMixReadPercent.addEventListener('input', () => {
                    const readPercent = this.rwMixReadPercent.value;
                    const writePercent = 100 - readPercent;
                    this.rwMixDisplay.textContent = `${readPercent}% 读 / ${writePercent}% 写`;
                });
                
                // 按钮事件
                this.startButton.addEventListener('click', () => this.showWarningModal());
                this.stopButton.addEventListener('click', () => this.stopTest());
                this.refreshDevicesButton.addEventListener('click', () => this.loadDevices());
                this.clearLogsButton.addEventListener('click', () => this.clearLogs());
                
                // 模态框事件
                this.confirmButton.addEventListener('click', () => this.startTest());
                this.cancelButton.addEventListener('click', () => this.hideWarningModal());
                
                // 点击模态框外部关闭
                this.warningModal.addEventListener('click', (e) => {
                    if (e.target === this.warningModal) {
                        this.hideWarningModal();
                    }
                });
            }
            
            async loadDevices() {
                try {
                    const response = await fetch('/api/devices');
                    const devices = await response.json();
                    
                    this.targetDevice.innerHTML = '';
                    
                    devices.forEach(device => {
                        const option = document.createElement('option');
                        option.value = device.name;
                        option.textContent = `${device.name} (${device.size})`;
                        this.targetDevice.appendChild(option);
                    });
                    
                    if (devices.length === 0) {
                        const option = document.createElement('option');
                        option.value = '/dev/nvme1n1';
                        option.textContent = '/dev/nvme1n1 (未检测到)';
                        this.targetDevice.appendChild(option);
                    }
                    
                } catch (error) {
                    console.error('加载设备列表失败:', error);
                    this.addLog('错误', '加载设备列表失败');
                }
            }
            
            getConfig() {
                const blockSizes = [];
                const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
                checkboxes.forEach(checkbox => {
                    blockSizes.push(checkbox.value);
                });
                
                return {
                    target_device: this.targetDevice.value,
                    precondition_bs: this.preconditionBs.value,
                    precondition_runtime: this.preconditionRuntime.value,
                    block_sizes: blockSizes,
                    rw_mix_read_percent: parseInt(this.rwMixReadPercent.value),
                    runtime: parseInt(this.runtime.value),
                    ramp_time: parseInt(this.rampTime.value),
                    num_jobs: parseInt(this.numJobs.value),
                    iodepth_per_job: parseInt(this.iodepthPerJob.value),
                    log_interval: 5000
                };
            }
            
            showWarningModal() {
                this.warningModal.style.display = 'block';
            }
            
            hideWarningModal() {
                this.warningModal.style.display = 'none';
            }
            
            async startTest() {
                this.hideWarningModal();
                
                const config = this.getConfig();
                
                if (config.block_sizes.length === 0) {
                    alert('请至少选择一个测试块大小');
                    return;
                }
                
                try {
                    const response = await fetch('/api/start', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(config)
                    });
                    
                    const result = await response.json();
                    
                    if (response.ok) {
                        this.addLog('系统', '测试已启动');
                    } else {
                        this.addLog('错误', result.error || '启动测试失败');
                    }
                    
                } catch (error) {
                    console.error('启动测试失败:', error);
                    this.addLog('错误', '启动测试失败: ' + error.message);
                }
            }
            
            async stopTest() {
                try {
                    const response = await fetch('/api/stop', {
                        method: 'POST'
                    });
                    
                    const result = await response.json();
                    this.addLog('系统', result.message || '测试已停止');
                    
                } catch (error) {
                    console.error('停止测试失败:', error);
                    this.addLog('错误', '停止测试失败: ' + error.message);
                }
            }
            
            startStatusPolling() {
                this.statusInterval = setInterval(async () => {
                    try {
                        const response = await fetch('/api/status');
                        const status = await response.json();
                        this.updateTestStatus(status);
                    } catch (error) {
                        console.error('获取状态失败:', error);
                    }
                }, 2000); // 每2秒轮询一次
            }
            
            updateTestStatus(status) {
                // 更新运行状态
                this.testRunning = status.running;
                this.startButton.disabled = status.running;
                this.stopButton.disabled = !status.running;
                
                // 更新状态指示器
                const indicator = this.testStatus.querySelector('.status-indicator');
                indicator.className = `status-indicator ${status.running ? 'running' : 'idle'}`;
                
                // 更新状态信息
                this.testStatus.innerHTML = `<span class="status-indicator ${status.running ? 'running' : 'idle'}"></span>${status.running ? '运行中' : '已停止'}`;
                this.testStage.textContent = status.stage || '-';
                this.startTime.textContent = status.start_time ? 
                    new Date(status.start_time).toLocaleString() : '-';
                
                // 更新进度条
                this.progressFill.style.width = `${status.progress}%`;
                this.progressText.textContent = `${status.progress}%`;
                
                // 更新日志
                if (status.logs && status.logs.length > 0) {
                    const currentLogCount = this.logContainer.children.length;
                    const newLogs = status.logs.slice(currentLogCount);
                    
                    newLogs.forEach(log => {
                        this.addLogEntry(log.timestamp, log.message);
                    });
                }
            }
            
            addLog(source, message) {
                const timestamp = new Date().toISOString();
                this.addLogEntry(timestamp, `[${source}] ${message}`);
            }
            
            addLogEntry(timestamp, message) {
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';
                
                const timeSpan = document.createElement('span');
                timeSpan.className = 'log-timestamp';
                timeSpan.textContent = new Date(timestamp).toLocaleTimeString();
                
                const messageSpan = document.createElement('span');
                messageSpan.textContent = message;
                
                logEntry.appendChild(timeSpan);
                logEntry.appendChild(messageSpan);
                this.logContainer.appendChild(logEntry);
                
                // 自动滚动
                this.logContainer.scrollTop = this.logContainer.scrollHeight;
                
                // 限制日志数量
                while (this.logContainer.children.length > 500) {
                    this.logContainer.removeChild(this.logContainer.firstChild);
                }
            }
            
            clearLogs() {
                this.logContainer.innerHTML = '';
                this.addLog('系统', '日志已清空');
            }
        }

        // 页面加载完成后初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new FIOController();
        });
    </script>
</body>
</html>
