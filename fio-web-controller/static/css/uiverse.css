/* UIverse 风格的组件 */

/* 玻璃态按钮 */
.btn-glass {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    border-radius: 12px;
    color: white;
    padding: 12px 24px;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-glass:hover {
    background: rgba(255, 255, 255, 0.35);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 霓虹灯效果按钮 */
.btn-neon {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-neon:hover {
    color: white;
    box-shadow: 
        0 0 20px #667eea,
        0 0 40px #667eea,
        0 0 60px #667eea;
}

.btn-neon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, #667eea, transparent);
    transition: left 0.5s;
}

.btn-neon:hover::before {
    left: 100%;
}

/* 3D 卡片效果 */
.card-3d {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.1),
        0 8px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    transform-style: preserve-3d;
}

.card-3d:hover {
    transform: rotateX(5deg) rotateY(5deg) translateZ(10px);
    box-shadow: 
        0 8px 16px rgba(0, 0, 0, 0.15),
        0 16px 32px rgba(0, 0, 0, 0.15);
}

/* 渐变边框输入框 */
.input-gradient {
    position: relative;
    background: white;
    border-radius: 8px;
    padding: 2px;
    background: linear-gradient(45deg, #667eea, #764ba2, #667eea);
    background-size: 300% 300%;
    animation: gradient-shift 3s ease infinite;
}

.input-gradient input {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 6px;
    background: white;
    outline: none;
}

@keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 液体按钮效果 */
.btn-liquid {
    position: relative;
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    background: #667eea;
    color: white;
    font-weight: 600;
    cursor: pointer;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-liquid::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn-liquid:hover::before {
    width: 300px;
    height: 300px;
}

/* 脉冲加载器 */
.pulse-loader {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    animation: pulse-scale 1s ease-in-out infinite;
}

@keyframes pulse-scale {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

/* 波浪进度条 */
.wave-progress {
    position: relative;
    width: 100%;
    height: 20px;
    background: #e2e8f0;
    border-radius: 10px;
    overflow: hidden;
}

.wave-progress::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 200%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(102, 126, 234, 0.3), 
        transparent);
    animation: wave-move 2s linear infinite;
}

@keyframes wave-move {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(50%); }
}

/* 浮动标签输入框 */
.floating-label {
    position: relative;
    margin-bottom: 20px;
}

.floating-label input {
    width: 100%;
    padding: 12px 12px 12px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: transparent;
    outline: none;
    transition: all 0.3s ease;
}

.floating-label label {
    position: absolute;
    top: 12px;
    left: 12px;
    color: #a0aec0;
    transition: all 0.3s ease;
    pointer-events: none;
}

.floating-label input:focus,
.floating-label input:not(:placeholder-shown) {
    border-color: #667eea;
}

.floating-label input:focus + label,
.floating-label input:not(:placeholder-shown) + label {
    top: -8px;
    left: 8px;
    font-size: 12px;
    color: #667eea;
    background: white;
    padding: 0 4px;
}

/* 切换开关 */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 30px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 30px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #667eea;
}

input:checked + .toggle-slider:before {
    transform: translateX(30px);
}

/* 工具提示 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* 加载骨架屏 */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.skeleton-text {
    height: 16px;
    border-radius: 4px;
    margin-bottom: 8px;
}

.skeleton-button {
    height: 40px;
    border-radius: 8px;
    width: 120px;
}
