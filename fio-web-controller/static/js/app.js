// FIO Web Controller 前端应用
class FIOController {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.testRunning = false;
        
        this.initializeElements();
        this.bindEvents();
        this.connectWebSocket();
        this.loadDevices();
        this.loadDefaultConfig();
    }
    
    initializeElements() {
        // 表单元素
        this.targetDevice = document.getElementById('target_device');
        this.preconditionBs = document.getElementById('precondition_bs');
        this.preconditionRuntime = document.getElementById('precondition_runtime');
        this.rwMixReadPercent = document.getElementById('rw_mix_read_percent');
        this.rwMixDisplay = document.getElementById('rw_mix_display');
        this.runtime = document.getElementById('runtime');
        this.numJobs = document.getElementById('num_jobs');
        this.iodepthPerJob = document.getElementById('iodepth_per_job');
        this.rampTime = document.getElementById('ramp_time');
        
        // 控制按钮
        this.startButton = document.getElementById('start-test');
        this.stopButton = document.getElementById('stop-test');
        this.refreshDevicesButton = document.getElementById('refresh-devices');
        this.clearLogsButton = document.getElementById('clear-logs');
        
        // 状态显示
        this.testStatus = document.getElementById('test-status');
        this.testStage = document.getElementById('test-stage');
        this.startTime = document.getElementById('start-time');
        this.progressFill = document.getElementById('progress-fill');
        this.progressText = document.getElementById('progress-text');
        this.logContainer = document.getElementById('log-container');
        this.autoScroll = document.getElementById('auto-scroll');
        
        // 模态框
        this.warningModal = document.getElementById('warning-modal');
        this.confirmButton = document.getElementById('confirm-test');
        this.cancelButton = document.getElementById('cancel-test');
    }
    
    bindEvents() {
        // 滑块事件
        this.rwMixReadPercent.addEventListener('input', () => {
            const readPercent = this.rwMixReadPercent.value;
            const writePercent = 100 - readPercent;
            this.rwMixDisplay.textContent = `${readPercent}% 读 / ${writePercent}% 写`;
        });
        
        // 按钮事件
        this.startButton.addEventListener('click', () => this.showWarningModal());
        this.stopButton.addEventListener('click', () => this.stopTest());
        this.refreshDevicesButton.addEventListener('click', () => this.loadDevices());
        this.clearLogsButton.addEventListener('click', () => this.clearLogs());
        
        // 模态框事件
        this.confirmButton.addEventListener('click', () => this.startTest());
        this.cancelButton.addEventListener('click', () => this.hideWarningModal());
        
        // 点击模态框外部关闭
        this.warningModal.addEventListener('click', (e) => {
            if (e.target === this.warningModal) {
                this.hideWarningModal();
            }
        });
        
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideWarningModal();
            }
        });
    }
    
    connectWebSocket() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            console.log('WebSocket 连接成功');
            this.isConnected = true;
            this.addLog('系统', 'WebSocket 连接成功');
        });
        
        this.socket.on('disconnect', () => {
            console.log('WebSocket 连接断开');
            this.isConnected = false;
            this.addLog('系统', 'WebSocket 连接断开');
        });
        
        this.socket.on('test_update', (data) => {
            this.updateTestStatus(data);
        });
    }
    
    async loadDevices() {
        try {
            const response = await fetch('/api/devices');
            const devices = await response.json();
            
            // 清空现有选项
            this.targetDevice.innerHTML = '';
            
            // 添加设备选项
            devices.forEach(device => {
                const option = document.createElement('option');
                option.value = device.name;
                option.textContent = `${device.name} (${device.size})`;
                this.targetDevice.appendChild(option);
            });
            
            if (devices.length === 0) {
                const option = document.createElement('option');
                option.value = '/dev/nvme1n1';
                option.textContent = '/dev/nvme1n1 (未检测到)';
                this.targetDevice.appendChild(option);
            }
            
        } catch (error) {
            console.error('加载设备列表失败:', error);
            this.addLog('错误', '加载设备列表失败');
        }
    }
    
    async loadDefaultConfig() {
        try {
            const response = await fetch('/api/config');
            const config = await response.json();
            
            // 设置默认值
            this.targetDevice.value = config.target_device;
            this.preconditionBs.value = config.precondition_bs;
            this.preconditionRuntime.value = config.precondition_runtime;
            this.rwMixReadPercent.value = config.rw_mix_read_percent;
            this.runtime.value = config.runtime;
            this.numJobs.value = config.num_jobs;
            this.iodepthPerJob.value = config.iodepth_per_job;
            this.rampTime.value = config.ramp_time;
            
            // 更新显示
            this.rwMixReadPercent.dispatchEvent(new Event('input'));
            
            // 设置块大小复选框
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = config.block_sizes.includes(checkbox.value);
            });
            
        } catch (error) {
            console.error('加载默认配置失败:', error);
        }
    }
    
    getConfig() {
        // 获取选中的块大小
        const blockSizes = [];
        const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
        checkboxes.forEach(checkbox => {
            blockSizes.push(checkbox.value);
        });
        
        return {
            target_device: this.targetDevice.value,
            precondition_bs: this.preconditionBs.value,
            precondition_runtime: this.preconditionRuntime.value,
            block_sizes: blockSizes,
            rw_mix_read_percent: parseInt(this.rwMixReadPercent.value),
            runtime: parseInt(this.runtime.value),
            ramp_time: parseInt(this.rampTime.value),
            num_jobs: parseInt(this.numJobs.value),
            iodepth_per_job: parseInt(this.iodepthPerJob.value),
            log_interval: 5000
        };
    }
    
    showWarningModal() {
        this.warningModal.style.display = 'block';
    }
    
    hideWarningModal() {
        this.warningModal.style.display = 'none';
    }
    
    async startTest() {
        this.hideWarningModal();
        
        const config = this.getConfig();
        
        // 验证配置
        if (config.block_sizes.length === 0) {
            alert('请至少选择一个测试块大小');
            return;
        }
        
        try {
            const response = await fetch('/api/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(config)
            });
            
            const result = await response.json();
            
            if (response.ok) {
                this.addLog('系统', '测试已启动');
                this.setTestRunning(true);
            } else {
                this.addLog('错误', result.error || '启动测试失败');
            }
            
        } catch (error) {
            console.error('启动测试失败:', error);
            this.addLog('错误', '启动测试失败: ' + error.message);
        }
    }
    
    async stopTest() {
        try {
            const response = await fetch('/api/stop', {
                method: 'POST'
            });
            
            const result = await response.json();
            this.addLog('系统', result.message || '测试已停止');
            
        } catch (error) {
            console.error('停止测试失败:', error);
            this.addLog('错误', '停止测试失败: ' + error.message);
        }
    }
    
    setTestRunning(running) {
        this.testRunning = running;
        this.startButton.disabled = running;
        this.stopButton.disabled = !running;
        
        // 更新状态指示器
        const statusIndicator = document.querySelector('.status-indicator');
        if (statusIndicator) {
            statusIndicator.className = `status-indicator ${running ? 'running' : 'idle'}`;
        }
    }
    
    updateTestStatus(status) {
        // 更新运行状态
        this.setTestRunning(status.running);
        
        // 更新状态信息
        this.testStatus.textContent = status.running ? '运行中' : '已停止';
        this.testStage.textContent = status.stage || '-';
        this.startTime.textContent = status.start_time ? 
            new Date(status.start_time).toLocaleString() : '-';
        
        // 更新进度条
        this.progressFill.style.width = `${status.progress}%`;
        this.progressText.textContent = `${status.progress}%`;
        
        // 更新日志
        if (status.logs && status.logs.length > 0) {
            const lastLogCount = this.logContainer.children.length;
            const newLogs = status.logs.slice(lastLogCount);
            
            newLogs.forEach(log => {
                this.addLogEntry(log.timestamp, log.message);
            });
        }
    }
    
    addLog(source, message) {
        const timestamp = new Date().toISOString();
        this.addLogEntry(timestamp, `[${source}] ${message}`);
    }
    
    addLogEntry(timestamp, message) {
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        
        const timeSpan = document.createElement('span');
        timeSpan.className = 'log-timestamp';
        timeSpan.textContent = new Date(timestamp).toLocaleTimeString();
        
        const messageSpan = document.createElement('span');
        messageSpan.className = 'log-message';
        messageSpan.textContent = message;
        
        logEntry.appendChild(timeSpan);
        logEntry.appendChild(messageSpan);
        this.logContainer.appendChild(logEntry);
        
        // 自动滚动
        if (this.autoScroll.checked) {
            this.logContainer.scrollTop = this.logContainer.scrollHeight;
        }
        
        // 限制日志数量
        while (this.logContainer.children.length > 1000) {
            this.logContainer.removeChild(this.logContainer.firstChild);
        }
    }
    
    clearLogs() {
        this.logContainer.innerHTML = '';
        this.addLog('系统', '日志已清空');
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new FIOController();
});
