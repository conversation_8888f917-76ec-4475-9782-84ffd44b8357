# FIO NVMe 硬盘测试 Web 控制器

一个基于Web的FIO硬盘性能测试控制器，允许局域网内的其他机器通过浏览器远程控制和监控硬盘测试。

## 功能特性

- 🌐 **Web界面控制**: 通过现代化的Web界面配置和控制FIO测试
- 📊 **实时监控**: WebSocket实时显示测试进度和日志
- 🎛️ **参数配置**: 可视化配置所有FIO测试参数
- 🔒 **安全确认**: 测试前的安全警告和确认机制
- 📱 **响应式设计**: 支持桌面和移动设备访问
- 🎨 **现代UI**: 使用UIverse风格的现代化组件

## 系统要求

- Ubuntu 18.04 或更高版本
- Python 3.6+
- FIO 工具
- 可选: gnuplot (用于生成性能图表)

## 安装和使用

### 1. 安装系统依赖

```bash
# 更新包管理器
sudo apt update

# 安装必要工具
sudo apt install python3 python3-pip python3-venv fio

# 可选：安装gnuplot用于生成图表
sudo apt install gnuplot
```

### 2. 启动Web服务器

有两种启动方式：

#### 方式一：简化版（推荐）
```bash
# 进入项目目录
cd fio-web-controller

# 运行简化版启动脚本（不需要额外依赖）
./start_simple.sh
```

#### 方式二：完整版
```bash
# 进入项目目录
cd fio-web-controller

# 运行完整版启动脚本（需要安装Flask等依赖）
./start.sh
```

简化版启动脚本会自动：
- 检查系统环境
- 使用Python内置库启动Web服务器
- 无需安装额外的Python依赖

完整版启动脚本会自动：
- 检查系统环境
- 创建Python虚拟环境
- 安装Python依赖（Flask、SocketIO等）
- 启动Web服务器

### 3. 访问Web界面

启动后，可以通过以下地址访问：

- **本地访问**: http://localhost:5000
- **局域网访问**: http://[服务器IP]:5000

### 4. 配置防火墙

如果需要局域网访问，请确保防火墙允许5000端口：

```bash
sudo ufw allow 5000
```

## 使用说明

### 配置测试参数

1. **目标设备**: 选择要测试的NVMe设备
2. **预写配置**: 设置预写块大小和时长
3. **测试块大小**: 选择要测试的块大小（4K、128K、1M）
4. **读写比例**: 调整读写混合比例
5. **性能参数**: 配置测试时长、线程数、队列深度等

### 执行测试

1. 点击"开始测试"按钮
2. 阅读并确认安全警告
3. 实时监控测试进度和日志
4. 测试完成后查看结果文件

### 安全注意事项

⚠️ **重要警告**: 
- FIO测试会对目标设备进行破坏性读写操作
- 设备上的所有数据都将丢失
- 请确保选择正确的设备并备份重要数据
- 建议在专用测试环境中运行

## 项目结构

```
fio-web-controller/
├── app.py                 # Flask后端应用
├── requirements.txt       # Python依赖
├── start.sh              # 启动脚本
├── README.md             # 说明文档
├── templates/
│   └── index.html        # 主页模板
└── static/
    ├── css/
    │   ├── style.css     # 主要样式
    │   └── uiverse.css   # UIverse组件样式
    └── js/
        └── app.js        # 前端JavaScript
```

## 技术栈

- **后端**: Python Flask + Flask-SocketIO
- **前端**: HTML5 + CSS3 + JavaScript (ES6)
- **实时通信**: WebSocket
- **UI组件**: UIverse风格组件
- **测试工具**: FIO

## API接口

### REST API

- `GET /api/config` - 获取默认配置
- `GET /api/devices` - 获取可用设备列表
- `GET /api/status` - 获取测试状态
- `POST /api/start` - 启动测试
- `POST /api/stop` - 停止测试

### WebSocket事件

- `test_update` - 测试状态更新

## 故障排除

### 常见问题

1. **无法访问Web界面**
   - 检查防火墙设置
   - 确认服务器正在运行
   - 检查IP地址和端口

2. **找不到设备**
   - 确认NVMe设备已正确安装
   - 检查设备权限
   - 使用 `lsblk` 命令查看设备列表

3. **测试失败**
   - 检查FIO是否正确安装
   - 确认设备权限
   - 查看日志获取详细错误信息

### 日志查看

Web界面提供实时日志显示，也可以查看系统日志：

```bash
# 查看应用日志
tail -f /var/log/syslog | grep fio

# 查看设备状态
lsblk
dmesg | grep nvme
```

## 许可证

本项目仅供学习和测试使用。使用时请遵守相关法律法规，作者不承担任何责任。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

---

**注意**: 请在使用前仔细阅读所有安全警告，确保在安全的测试环境中运行。
