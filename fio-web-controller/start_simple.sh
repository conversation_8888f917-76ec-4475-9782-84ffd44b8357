#!/bin/bash

# FIO Web Controller 简化版启动脚本

echo "=== FIO Web Controller 简化版启动脚本 ==="
echo

# 检查Python版本
echo "检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到 python3，请先安装 Python 3"
    exit 1
fi

PYTHON_VERSION=$(python3 --version 2>&1 | cut -d' ' -f2 | cut -d'.' -f1,2)
echo "Python 版本: $PYTHON_VERSION"

# 检查fio是否安装
echo "检查fio工具..."
if ! command -v fio &> /dev/null; then
    echo "警告: 未找到 fio 工具"
    echo "请运行以下命令安装 fio:"
    echo "  sudo apt update"
    echo "  sudo apt install fio"
    echo
    read -p "是否继续启动Web服务器? (y/N): " continue_anyway
    if [[ ! $continue_anyway =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 检查gnuplot（可选）
if ! command -v gnuplot &> /dev/null; then
    echo "提示: 未找到 gnuplot，将无法生成性能图表"
    echo "可选安装命令: sudo apt install gnuplot"
fi

# 检查权限
echo "检查权限..."
if [ ! -w "/tmp" ]; then
    echo "错误: 无法写入 /tmp 目录"
    exit 1
fi

# 获取本机IP地址
LOCAL_IP=$(hostname -I | awk '{print $1}' 2>/dev/null || echo "未知")

echo
echo "=== 启动信息 ==="
echo "本地访问地址: http://localhost:5000"
if [ "$LOCAL_IP" != "未知" ]; then
    echo "局域网访问地址: http://$LOCAL_IP:5000"
fi
echo
echo "请确保防火墙允许5000端口的访问"
echo "Ubuntu防火墙配置命令:"
echo "  sudo ufw allow 5000"
echo
echo "按 Ctrl+C 停止服务器"
echo "==================="
echo

# 启动应用
python3 simple_server.py
